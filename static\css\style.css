/* Importing the Font from Google Fonts */
@import url('https://fonts.googleapis.com/css2?family=Montserrat:ital,wght@0,100..900;1,100..900&family=Open+Sans:ital,wght@0,300..800;1,300..800&family=Playfair+Display:ital,wght@0,400..900;1,400..900&family=Raleway:ital,wght@0,100..900;1,100..900&family=Roboto:ital,wght@0,100..900;1,100..900&display=swap');

/* =================================
   THEME CONFIGURATION
   Main color scheme and core variables
   ================================= */

:root {
  /* Brand Colors 
       Primary palette defining visual identity */
  --primary-color: #001a73; /* Core UI elements */
  --secondary-color: #ff7b29; /* Action items & emphasis */
  --accent-color: #c85c86; /* Decorative highlights */

  /* Interface Colors 
       Base colors for UI components */
  --dark-text: #191919; /* Primary dark text */
  --light-text: #f5f6f3; /* Primary light text */
  --light-grey-text: #9198a1; /*Light grey text*/
  --dark-grey-text: #738a90; /*Dark grey text*/
  --dark-bg: #011627; /* Dark mode background */
  --black-bg: #000000; /* Base black background */
  --light-bg: #f8fafa; /* Light mode background */

  /* Feedback Colors 
       User interaction states */
  --success: #00614a; /* Success indicators */
  --error: #dd1c1a; /* Error messages */
  --warning: #f79818; /* Warning alerts */

  /* Interactive Colors 
       State changes for elements */
  --hover: #6161ff; /* Hover state */
}

/* =================================
   EDGE CASES
   Special Circumstances 
   ================================= */

/* Text Colors*/
.text-secondary,
.text-muted {
  color: var(--dark-text) !important;
}

/*Button Styles*/
#submit_btn {
  background: var(--secondary-color);
}

#submit_btn:hover {
  background: var(--hover);
}

.post-subtitle a {
  /* Styles for the author link */
  color: var(--secondary-color);
}

/*Author Link Styles*/
.post-subtitle a:hover {
  /* Styles for the author link */
  text-decoration: underline;
  color: var(--hover);
}

/*Delete Confirmation*/
#confirm_delete {
  color: var(--light-text);
}

/* Sign-up Button Styles */
.btn.btn-primary.btn-lg,
#black-text-home {
  background-color: var(--secondary-color);
  color: var(--dark-text);
  border: none;
  padding: 4px 8px;
  border-radius: 4px;
  text-transform: uppercase;
  font-weight: bold;
  transition: background-color 0.3s, transform 0.1s;
}

/* Style for signup button hover state */
.btn.btn-primary.btn-lg:hover,
#black-text-home:hover {
  background-color: var(--hover);
  color: var(--light-text);
  transform: scale(1.05);
}

/*Color Labels: Create Blog Post*/
#blogPostForm label {
  color: var(--light-text);
  font-family: 'Raleway', sans-serif;
  font-weight: 600;
}

/*Alert Container*/
.alert-container {
  position: absolute;
  top: 50px;
  left: 0;
  right: 0;
  z-index: 99;
  width: 100%;
  padding: 2px;
}

/*Typography Colors*/

#profile_edit_heading,
h4.text-center.medium {
  color: var(--dark-text);
}

#bookmared-posts,
h1.my-4.text-center,
h2,
label {
  color: var(--light-text);
}

input {
  color: var(--dark-grey-text);
}

.text-secondary {
  color: var(--dark-text) !important;
}

.text-muted {
  color: var(--dark-text) !important;
}

#submit_btn {
  background: var(--secondary-color);
}

#submit_btn:hover {
  background: var(--hover);
}

.post-subtitle a {
  /* Styles for the author link */
  color: var(--secondary-color);
}

.post-subtitle a:hover {
  /* Styles for the author link */
  text-decoration: underline;
  color: var(--hover);
}

/*Better Delete Confirmation Design*/
#confirm_delete {
  color: var(--light-text);
}

/* Style to make signup button text black on signup page */
.btn.btn-primary.btn-lg,
#black-text-home {
  background-color: var(--secondary-color);
  color: var(--dark-text);
  border: none;
  padding: 4px 8px;
  border-radius: 4px;
  text-transform: uppercase;
  font-weight: bold;
  transition: background-color 0.3s, transform 0.1s;
}

/* Style for signup button hover state */
.btn.btn-primary.btn-lg:hover,
#black-text-home:hover {
  background-color: var(--hover);
  color: var(--light-text);
  transform: scale(1.05);
}

/*Color Labels: Create Blog Post*/
#blogPostForm label {
  color: var(--light-text);
  font-family: 'Raleway', sans-serif;
  font-weight: 600;
}

.alert-container {
  position: absolute;
  top: 50px;
  left: 0;
  right: 0;
  z-index: 99;
  width: 100%;
  padding: 2px;
}

#profile_edit_heading {
  color: var(--dark-text);
}

#bookmared-posts {
  color: var(--light-text);
}

h1.my-4.text-center {
  color: var(--light-text);
}

h4.text-center.medium {
  color: var(--dark-text);
}

label {
  color: var(--light-text);
}

h2 {
  color: var(--light-text);
}

input {
  color: var(--dark-grey-text);
}

/* =================================
   BASE LAYOUT
   Core page structure
   ================================= */

body {
  font-family: 'Open Sans', sans-serif;
  background-color: var(--dark-bg);
  background-repeat: no-repeat;
  background-attachment: fixed;
  background-size: cover;
  min-height: 100vh;
}

/* =================================
   NAVIGATION COMPONENTS
   Header and menu elements
   ================================= */

/* Nav Container */
.navbar {
  background-color: var(--black-bg);
  box-shadow: 43px -130px 61px -136px rgba(0, 0, 0, 0.75) inset;
  -webkit-box-shadow: 43px -130px 61px -136px rgba(0, 0, 0, 0.75) inset;
  -moz-box-shadow: 43px -130px 61px -136px rgba(0, 0, 0, 0.75) inset;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.navbar-dark {
  color: var(--light-text);
}

/* Branding Elements */
.navbar-logo {
  font-family: Lato, sans-serif;
  font-size: 1.4rem;
  font-weight: 700;
  color: var(--secondary-color);
  text-decoration: none;
  transition: transform 0.1s ease;
}

/* Logo Interactions */
.navbar-logo:hover {
  color: var(--hover);
  transform: scale(1.1);
}

.nav-link:hover {
  color: var(--hover);
}

/* User Menu Icon */
.nav-link.dropdown-toggle i.fas.fa-user-circle {
  color: var(--secondary-color);
  transition: transform 0.1s ease;
}

.nav-link.dropdown-toggle i.fas.fa-user-circle:hover {
  transform: scale(1.1);
  color: var(--hover);
}

/* =================================
   FOOTER SECTION
   Page footer elements
   ================================= */

.footer {
  background-color: var(--black-bg);
  color: var(--secondary-color);
}

/* =================================
   FORM ELEMENTS
   Form styling and validation feedback
   ================================= */

/* Form Error Messages
   Custom styling for form validation errors */
.my_errorlist {
  color: var(--error); /* Uses your defined error color #dd1c1a */
  margin-top: 5px;
  margin-bottom: 5px;
  list-style: none;
  padding-left: 0;
  font-size: 0.875rem; /* Slightly smaller than normal text */
  font-weight: 500; /* Medium weight for better readability */
}

.my_errorlist li {
  margin-bottom: 2px; /* Small spacing between multiple errors */
}

/* =================================
   INTERACTIVE ELEMENTS
   Defines styles for buttons and clickable components
   ================================= */

/* Primary & Secondary Buttons
   Basic styling for main action buttons */
.btn-primary,
.btn-secondary {
  background-color: var(--secondary-color);
  color: var(--dark-text);
  border: none;
  padding: 4px 8px;
  border-radius: 4px;
  text-transform: uppercase;
  font-weight: bold;
  transition: background-color 0.3s, transform 0.1s;
}

/* Danger Button
      Styling for destructive actions */
.btn-danger {
  background-color: var(--warning);
  color: var(--light-text);
  border: none;
  padding: 8px 16px;
  border-radius: 4px;
  text-transform: uppercase;
  font-weight: bold;
  transition: background-color 0.3s;
}

/* Hover States
      Visual feedback for user interaction */

.btn-primary:hover,
.btn-signup:hover,
.btn-secondary:hover,
.btn-danger:hover {
  background-color: var(--hover);
  color: var(--light-text);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

/* Active States
      Visual feedback during click/tap */
.btn-primary:active,
.btn-signup:active,
.btn-secondary:active,
.btn-danger:active {
  background-color: var(--secondary-color);
  color: var(--light-text-gr);
  transform: scale(0.95);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

/* Focus States
      Accessibility and keyboard navigation */
.btn-primary:focus,
.btn-signup:focus,
.btn-secondary:focus,
.btn-danger:focus {
  background-color: var(--accent-color);
  color: var(--light-text-gr);
  outline: none;
  box-shadow: 0 0 0 2px var(--bg);
}

/* Secondary Button Variants
      Alternative styling for secondary actions */
.btn-secondary {
  background-color: var(--black-bg);
  color: var(--light-text);
}
.btn-secondary:hover {
  background-color: var(--hover);
}
.btn-secondary:active {
  background-color: var(--success);
  color: var(--light-text);
}

/* Selected Category Button - active */
.btn.ctg.active {
  background-color: var(--warning);
  color: var(--dark-text);
}

/* Danger Button Variants
      Styling for destructive actions */
.btn-danger {
  background-color: var(--warning);
  color: var(--light-text-gr);
}

/* Like Button
      Special styling for social interaction */
.btn-like {
  border: none;
  background: transparent;
}

.btn-like:hover,
.btn-like:active {
  color: var(--accent-color);
  background: transparent;
  border: none;
}

.fa-heart {
  color: var(--secondary-color);
}

/* ==================================
   Link Styling
   ================================== */

/* Core link styles - defines base appearance for all link types
   Uses CSS custom properties for maintainable theming */
.link,
.post-link,
.page-link {
  color: var(--dark-text); /* Uses theme color variable */
  text-decoration: none; /* Removes default underline */
  text-decoration-style: double;
}

/* Hover states - provides visual feedback on interaction
      Maintains consistency across different link types */
.link:hover,
.post-link:hover,
.page-link:hover {
  color: var(--secondary-color); /* Uses theme hover color */
  text-decoration: none; /* Adds underline on hover */
  text-transform: capitalize;
  text-decoration-line: underline;
}

.custom-page-number {
  background: none;
  color: var(--accent-color);
  padding: 5px;
  margin: 5px;
}

/* Global link styles - applies to all <a> elements
      Provides default styling for any links not covered by specific classes */
a {
  color: var(--dark-text); /* Default link color */
  text-decoration: none; /* Removes default underline */
}

/* Global hover and active states
      Enhances user interaction feedback */
a:hover {
  color: var(--hover); /* Changes color on hover */
  text-decoration: underline; /* Adds underline on hover */
}

a:active {
  color: var(--accent-color); /* Different color when clicked */
  text-decoration: underline; /* Maintains underline when active */
}

/* =================================
   CARD COMPONENTS
   Content card styling
   ================================= */

/* Card Base */
.card {
  border: none;
  background-color: var(--dark-bg);
  margin-bottom: 1rem;
  color: var(--dark-text);
}

.card:hover {
  box-shadow: 0 4px 8px 0 rgba(0, 0, 0, 0.2);
}

/* Card Content */
.card-body {
  padding: 1rem;
  -webkit-box-shadow: 2px 2px 4px 0px rgba(0, 0, 0, 0.2),
    inset 2px 2px 8px 0px rgba(0, 0, 0, 0.1),
    5px 2px 15px 5px rgba(18, 18, 18, 0.15);
  box-shadow: 2px 2px 4px 0px rgba(0, 0, 0, 0.2),
    inset 2px 2px 8px 0px rgba(0, 0, 0, 0.1),
    5px 2px 15px 5px rgba(18, 18, 18, 0.15);
  background-color: var(--light-bg);
}

/* Card Typography */
.card-title {
  color: var(--dark-text);
  font-size: 16px;
  margin-bottom: 0.5rem;
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
}

.card-text {
  color: var(--dark-text);
  font-size: 14px;
  text-overflow: ellipsis;
}

li {
  color: var(--dark-text);
}

/* =================================
   IMAGE HANDLING
   Image display and effects
   ================================= */

/* Image Ratios */
.aspect-ratio-3-2 {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: auto;
  object-fit: cover;
}

/* Image Container */
.image-container {
  position: relative;
  width: 100%;
  padding-top: 66.67%;
  overflow: hidden;
}

/* Image Hover */
.image-container .aspect-ratio-3-2 {
  transition: transform 0.5s ease;
}

.image-container:hover .aspect-ratio-3-2 {
  transform: scale(1.1);
}

/* Author Tag */
.author {
  bottom: -5px;
  left: 6px;
  color: var(--light-text);
  background-color: var(--dark-bg);
  padding: 3px;
  border-radius: 2px;
  font-size: 0.7rem;
}

/* Bookmark Button Styles */
.btn-bookmark {
  border: none;
  background: transparent;
  color: var(--secondary-color);
  margin-bottom: 4px;
}
.btn-bookmark:hover,
.btn-bookmark:active {
  background: transparent;
  border: none;
  color: var(--light-text-gr);
}
.fa-bookmark {
  color: var(--light-text-gr);
}
.post-bookmark {
  margin-left: 5%;
  color: var(--light-text-gr);
}

/* =================================
   MASTHEAD
   Header styling
   ================================= */

/* Header Container */
.masthead {
  margin-top: 10px;
  overflow: hidden;
  position: relative;
  display: inline-block;
  height: auto;
  width: 100%;
}

.masthead .row {
  display: flex;
}

/* Header Text */
.masthead-text {
  background-color: var(--dark-bg);
  color: var(--light-text);
  position: relative;
  overflow: hidden;
  box-shadow: 45px 13px 31px -16px rgba(0, 0, 0, 0.75) inset;
  -webkit-box-shadow: 45px 13px 31px -16px rgba(0, 0, 0, 0.75) inset;
  -moz-box-shadow: 45px 13px 31px -16px rgba(0, 0, 0, 0.75) inset;
}

/* Header Image */
.masthead-image {
  position: relative;
  overflow: hidden;
}

.masthead-image .image-container {
  height: 100%;
  width: 100%;
  overflow: hidden;
}
.masthead-text,
.masthead-image {
  padding: 0px;
}
.masthead-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}
.post-title {
  margin: 5%;
  text-align: center;
  font-size: 28px;
}
.post-subtitle {
  margin-left: 5%;
}

.card-excerpt {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  /* Number of lines to show */
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
  /* Other styles */
}
.blogpost-card {
  height: 460px;
  overflow: hidden;
}

/* CRISPY FORMS FIELD TARGETING */
.form-control {
  margin-bottom: 10px;
  font-size: 12px;
}
.form-control[type='file'],
.form-control[name='bio'] {
  margin-bottom: 20px;
}

/* Targeting fields within crispy forms */
.form-control,
select,
textarea {
  color: var(--dark-text);
  border: 1px solid black;
}

/* =================================
   PROFILE COMPONENTS
   User profile styling
   ================================= */

/* Profile Layout */
.profile-details {
  margin: 0 auto;
  padding: 20px;
}

/*=================================
  PROFILE TABLE STYLES
  Core table styling for profile display
==================================*/

/* Base table structure */
.profile-table {
  width: 100%;
  border-collapse: collapse;
}

/* Cell spacing and borders */
.profile-table td {
  padding: 16px;
  border-bottom: 1px solid #dfc7c7;
}

/*=================================
  CATEGORY STYLING
  Label and header formatting
==================================*/

/* Top category labels - primary emphasis */
.category-label-top,
.category-label-top i {
  color: var(--dark-text);
  font-weight: bolder;
}

.category-label-top {
  text-align: left;
  width: 100%;
  line-height: 1.5;
}

/* Standard category labels - secondary emphasis */
.category-label {
  font-weight: bolder;
  text-align: right;
  width: 26%;
  line-height: 1.5;
}

.category-label,
.category-label i {
  color: var(--dark-text);
}

/* Category header styling */
.top-categories-heading {
  color: var(--dark-text);
  text-align: center;
  font-weight: bolder;
}

/* Vertical spacing between sections */
.table-spacing {
  height: 40px;
}

/* Category alignment rules */
.top-categories td:first-child {
  text-align: right;
}

/*=================================
  BIOGRAPHY SECTION
  Text styling for user bio
==================================*/

/* Bio text appearance */
.bio-text {
  color: var(--light-text-gr);
  font-weight: 100;
  font-size: small;
  width: 100%;
}

/* Minimum height for bio section */
.bio-height {
  min-height: 50px;
}

/*=================================
  PROFILE IMAGE
  Image container and sizing rules
==================================*/

/* Profile image constraints */
.profile-img {
  width: 100%;
  max-width: 230px;
  height: auto;
  display: inline-block;
  object-fit: cover;
}

/*=================================
  LOGIN PAGE LAYOUT
  Container and component positioning
==================================*/

/* Main container setup */
.login-page-container {
  position: relative;
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
}

/* Login form elements */
.h2-login {
  margin: -16px;
  font-size: 26px;
}

.card-login {
  margin-top: 6px !important;
  margin-left: 8px !important;
  margin-right: 8px !important;
  padding: 8px;
}

/* Branding text  */
.login-nederlearn {
  font-size: 57px;
  font-weight: 700;
  color: var(--dark-text);
}

/* =================================
  RESPONSIVE DESIGN
  Media queries for different screens
==================================*/

/* Desktop/large screen styles */
.login-logo img {
  height: 580px;
  margin-top: -125px;
  margin-bottom: 10px;
  max-width: 100%;
}

/* Styles for large screens (992px and above) */
@media (min-width: 992px) {
  .masthead-image::before {
    content: '';
    position: absolute;
    right: 100%;
    top: 0;
    width: 50%;
    height: 100%;
    background-color: rgba(1, 43, 48);
    transform: skew(15deg);
    transform-origin: top right;
  }
}

/* Responsive adjustments for medium screens (tablets) */
@media (max-width: 992px) {
  .masthead .row {
    flex-wrap: wrap;
    /* Allows items to wrap as needed */
  }
  .masthead-text,
  .masthead-image {
    flex-basis: 100%;
  }
}

.blogpost-card {
  height: 518px;
}

.card-body {
  padding: 20px;
}
.top-categories td:first-child {
  text-align: left;
}
.profile-details {
  padding: 0px;
}
.profile-table td {
  padding: 4px;
}

.masthead-image {
  order: -1;
  /* Switches place for image (above the text) */
}
.btn {
  padding: 5px 10px;
  font-size: 0.8em;
}

/* Tablet/medium screen adaptations */
@media (max-width: 1024px) {
  .login-page-container {
    flex-direction: column;
    min-height: auto;
    padding: 10px 10px;
  }

  .login-logo img {
    height: auto;
    max-height: 40vh;
    margin-top: 0;
  }
}

/* Mobile/small screen adaptations */
@media (max-width: 767px) {
  .profile-img {
    margin-bottom: 20px;
  }

  .masthead-text h1 {
    font-size: 14px;
  }
  .masthead-text .post-subtitle,
  .masthead-text .post-bookmark {
    font-size: 0.9em;
  }

  .masthead-text h1 {
    font-size: 20px;
  }

  .masthead-image img {
    max-height: 300px;
  }

  .btn {
    padding: 5px 10px;
    font-size: 0.8em;
  }
}

/* Extra small screen adjustments */
@media (max-width: 580px) {
  h1 {
    font-size: 14px;
  }

  .login-logo img {
    max-height: 30vh;
  }

  .card-body {
    padding: 16px;
  }

  .top-categories td:first-child {
    text-align: left;
  }

  .profile-details {
    padding: 0px;
  }

  .profile-table td {
    padding: 4px;
  }

  .masthead-image {
    order: -1;
    /* Switches place for image (above the text) */
  }

  .blogpost-card {
    height: 508px;
  }
}

/* Mobile-first optimizations */
@media (max-width: 480px) {
  .login-logo img {
    max-height: 27vh;
    margin-top: -32px;
  }

  .h2-login {
    padding: 7px;
    margin: -22px;
  }

  .login-nederlearn {
    margin-top: 2px;
    font-size: 24px;
    margin-bottom: 32px !important;
  }

  .card-login {
    margin-top: 6px !important;
    margin-left: -18px !important;
    margin-right: -18px !important;
    padding: 8px;
  }

  .card-body {
    padding: 12px;
  }

  .blogpost-card {
    height: 470px;
  }
}
/* Responsive adjustments for min width 320px */
@media (max-width: 400px) {
  .blogpost-card {
    height: 420px;
  }
}

/*Color Labels: General*/
label {
  color: #000000;
  font-family: 'Raleway', sans-serif;
  font-weight: 600;
}
